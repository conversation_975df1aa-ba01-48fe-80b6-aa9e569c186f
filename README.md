# Test Addon - Connectivity Dashboard

This Odoo 17 addon provides a dashboard to monitor connectivity status between Odoo and Ozone systems.

## Features

- Real-time connectivity monitoring for Odoo and Ozone
- Dashboard with multiple view modes (Kanban, Form, Tree)
- Response time tracking
- Error message display
- Manual refresh functionality
- Environment-based configuration

## Setup Instructions

### 1. Virtual Environment
A virtual environment has been created in the `venv` folder. To activate it:

**Windows:**
```bash
venv\Scripts\activate
```

**Linux/Mac:**
```bash
source venv/bin/activate
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
1. Copy `.env.example` to `.env`
2. Update the configuration values in `.env`:
   - `OZONE_URL`: Your Ozone system URL
   - `OZONE_API_KEY`: API key for Ozone authentication
   - Other configuration as needed

### 4. Odoo Installation
1. Copy this addon to your Odoo addons directory
2. Update the addons path in your Odoo configuration
3. Restart Odoo server
4. Go to Apps menu and install "Test Addon - Connectivity Dashboard"

## Usage

1. Navigate to **Connectivity > Dashboard** in Odoo
2. Click **Refresh Status** to check current connectivity
3. View status indicators:
   - 🟢 Green: Connected
   - 🟡 Yellow: Disconnected
   - 🔴 Red: Error

## File Structure

```
test_addon/
├── models/
│   ├── __init__.py
│   └── connectivity_dashboard.py
├── views/
│   ├── connectivity_dashboard_views.xml
│   └── menu_views.xml
├── security/
│   └── ir.model.access.csv
├── static/
│   └── src/
│       ├── css/
│       │   └── dashboard.css
│       └── js/
│           └── dashboard.js
├── venv/                    # Virtual environment
├── __init__.py
├── __manifest__.py
├── requirements.txt
├── .env.example
└── README.md
```

## Model Details

### ConnectivityDashboard Model
- **Model Name**: `connectivity.dashboard`
- **Key Methods**:
  - `check_odoo_connectivity()`: Tests Odoo database connection
  - `check_ozone_connectivity()`: Tests Ozone API connection
  - `refresh_connectivity_status()`: Updates all connectivity status
  - `get_dashboard_data()`: Returns dashboard data for frontend

## Configuration

The addon reads configuration from a `.env` file in the addon root directory. Required variables:

- `OZONE_URL`: Base URL for Ozone API
- `OZONE_API_KEY`: Authentication key for Ozone
- Additional optional configuration for timeouts and intervals

## Troubleshooting

1. **Import Errors**: Ensure all dependencies are installed in the virtual environment
2. **Connection Issues**: Check `.env` configuration and network connectivity
3. **Permission Errors**: Verify user has access to the connectivity dashboard model
4. **View Issues**: Clear browser cache and restart Odoo server

## Next Steps

Once you provide the `.env` file with actual configuration values, the dashboard will be able to:
1. Test connectivity to your Ozone system
2. Display real-time status information
3. Track response times and errors
4. Provide a centralized monitoring interface
