import os
import requests
import logging
from datetime import datetime
from odoo import models, fields, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class ConnectivityDashboard(models.Model):
    _name = 'connectivity.dashboard'
    _description = 'Connectivity Dashboard'
    _rec_name = 'name'

    name = fields.Char(string='Dashboard Name', default='Connectivity Status', readonly=True)
    last_check = fields.Datetime(string='Last Check', readonly=True)
    odoo_status = fields.Selection([
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
        ('error', 'Error')
    ], string='Odoo Status', readonly=True)
    ozone_status = fields.Selection([
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected'),
        ('error', 'Error')
    ], string='Ozone Status', readonly=True)
    odoo_response_time = fields.Float(string='Odoo Response Time (ms)', readonly=True)
    ozone_response_time = fields.Float(string='Ozone Response Time (ms)', readonly=True)
    error_message = fields.Text(string='Error Message', readonly=True)

    @api.model
    def get_env_config(self):
        """Load configuration from .env file"""
        config = {}
        env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
        
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
        
        return config

    @api.model
    def check_odoo_connectivity(self):
        """Check Odoo database connectivity"""
        try:
            # Simple database query to check connectivity
            self.env.cr.execute("SELECT 1")
            result = self.env.cr.fetchone()
            if result:
                return 'connected', 0, None
            else:
                return 'disconnected', 0, 'No response from database'
        except Exception as e:
            _logger.error(f"Odoo connectivity check failed: {str(e)}")
            return 'error', 0, str(e)

    @api.model
    def check_ozone_connectivity(self):
        """Check Ozone system connectivity"""
        config = self.get_env_config()
        ozone_url = config.get('OZONE_URL')
        ozone_api_key = config.get('OZONE_API_KEY')
        
        if not ozone_url:
            return 'error', 0, 'OZONE_URL not configured in .env file'
        
        try:
            headers = {}
            if ozone_api_key:
                headers['Authorization'] = f'Bearer {ozone_api_key}'
            
            start_time = datetime.now()
            response = requests.get(
                f"{ozone_url}/health",  # Assuming a health endpoint
                headers=headers,
                timeout=10
            )
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            if response.status_code == 200:
                return 'connected', response_time, None
            else:
                return 'disconnected', response_time, f'HTTP {response.status_code}: {response.text}'
                
        except requests.exceptions.RequestException as e:
            _logger.error(f"Ozone connectivity check failed: {str(e)}")
            return 'error', 0, str(e)

    @api.model
    def refresh_connectivity_status(self):
        """Refresh the connectivity status for both systems"""
        try:
            # Check Odoo connectivity
            odoo_status, odoo_time, odoo_error = self.check_odoo_connectivity()
            
            # Check Ozone connectivity
            ozone_status, ozone_time, ozone_error = self.check_ozone_connectivity()
            
            # Combine error messages if any
            error_messages = []
            if odoo_error:
                error_messages.append(f"Odoo: {odoo_error}")
            if ozone_error:
                error_messages.append(f"Ozone: {ozone_error}")
            
            # Find or create dashboard record
            dashboard = self.search([], limit=1)
            if not dashboard:
                dashboard = self.create({
                    'name': 'Connectivity Status'
                })
            
            # Update the dashboard record
            dashboard.write({
                'last_check': fields.Datetime.now(),
                'odoo_status': odoo_status,
                'ozone_status': ozone_status,
                'odoo_response_time': odoo_time,
                'ozone_response_time': ozone_time,
                'error_message': '\n'.join(error_messages) if error_messages else False,
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
            
        except Exception as e:
            _logger.error(f"Failed to refresh connectivity status: {str(e)}")
            raise UserError(f"Failed to refresh connectivity status: {str(e)}")

    @api.model
    def get_dashboard_data(self):
        """Get dashboard data for the frontend"""
        dashboard = self.search([], limit=1)
        if not dashboard:
            # Create initial record
            dashboard = self.create({
                'name': 'Connectivity Status',
                'odoo_status': 'disconnected',
                'ozone_status': 'disconnected',
            })
        
        return {
            'last_check': dashboard.last_check,
            'odoo_status': dashboard.odoo_status,
            'ozone_status': dashboard.ozone_status,
            'odoo_response_time': dashboard.odoo_response_time,
            'ozone_response_time': dashboard.ozone_response_time,
            'error_message': dashboard.error_message,
        }
