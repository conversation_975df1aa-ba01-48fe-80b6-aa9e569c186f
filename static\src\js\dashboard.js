/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

class ConnectivityDashboard extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        
        this.state = useState({
            dashboardData: {},
            loading: false,
        });
        
        onMounted(() => {
            this.loadDashboardData();
        });
    }

    async loadDashboardData() {
        try {
            this.state.loading = true;
            const data = await this.orm.call(
                "connectivity.dashboard",
                "get_dashboard_data",
                []
            );
            this.state.dashboardData = data;
        } catch (error) {
            this.notification.add("Failed to load dashboard data", {
                type: "danger",
            });
            console.error("Dashboard loading error:", error);
        } finally {
            this.state.loading = false;
        }
    }

    async refreshStatus() {
        try {
            this.state.loading = true;
            await this.orm.call(
                "connectivity.dashboard",
                "refresh_connectivity_status",
                []
            );
            await this.loadDashboardData();
            this.notification.add("Connectivity status refreshed", {
                type: "success",
            });
        } catch (error) {
            this.notification.add("Failed to refresh status", {
                type: "danger",
            });
            console.error("Refresh error:", error);
        } finally {
            this.state.loading = false;
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 'connected':
                return 'status-connected';
            case 'disconnected':
                return 'status-disconnected';
            case 'error':
                return 'status-error';
            default:
                return 'status-disconnected';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'connected':
                return 'Connected';
            case 'disconnected':
                return 'Disconnected';
            case 'error':
                return 'Error';
            default:
                return 'Unknown';
        }
    }
}

ConnectivityDashboard.template = "test_addon.ConnectivityDashboard";

registry.category("actions").add("connectivity_dashboard", ConnectivityDashboard);
