/* Connectivity Dashboard Styles */

.o_kanban_dashboard .oe_kanban_global_click {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.o_kanban_dashboard .oe_kanban_global_click:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.connectivity-status-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-connected {
    background-color: #28a745;
}

.status-disconnected {
    background-color: #ffc107;
}

.status-error {
    background-color: #dc3545;
}

.response-time {
    font-size: 0.9em;
    color: #6c757d;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.refresh-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.refresh-button:hover {
    background-color: #0056b3;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    border: 1px solid #f5c6cb;
}
