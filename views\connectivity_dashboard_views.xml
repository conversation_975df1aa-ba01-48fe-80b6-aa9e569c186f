<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dashboard Form View -->
    <record id="view_connectivity_dashboard_form" model="ir.ui.view">
        <field name="name">connectivity.dashboard.form</field>
        <field name="model">connectivity.dashboard</field>
        <field name="arch" type="xml">
            <form string="Connectivity Dashboard" create="false" edit="false" delete="false">
                <header>
                    <button name="refresh_connectivity_status" 
                            string="Refresh Status" 
                            type="object" 
                            class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="last_check"/>
                            <field name="odoo_status" widget="badge" 
                                   decoration-success="odoo_status == 'connected'"
                                   decoration-danger="odoo_status == 'error'"
                                   decoration-warning="odoo_status == 'disconnected'"/>
                            <field name="odoo_response_time" attrs="{'invisible': [('odoo_response_time', '=', 0)]}"/>
                        </group>
                        <group>
                            <field name="ozone_status" widget="badge"
                                   decoration-success="ozone_status == 'connected'"
                                   decoration-danger="ozone_status == 'error'"
                                   decoration-warning="ozone_status == 'disconnected'"/>
                            <field name="ozone_response_time" attrs="{'invisible': [('ozone_response_time', '=', 0)]}"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('error_message', '=', False)]}">
                        <field name="error_message" nolabel="1" widget="text"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Tree View -->
    <record id="view_connectivity_dashboard_tree" model="ir.ui.view">
        <field name="name">connectivity.dashboard.tree</field>
        <field name="model">connectivity.dashboard</field>
        <field name="arch" type="xml">
            <tree string="Connectivity Dashboard" create="false" edit="false" delete="false">
                <field name="name"/>
                <field name="last_check"/>
                <field name="odoo_status" widget="badge" 
                       decoration-success="odoo_status == 'connected'"
                       decoration-danger="odoo_status == 'error'"
                       decoration-warning="odoo_status == 'disconnected'"/>
                <field name="ozone_status" widget="badge"
                       decoration-success="ozone_status == 'connected'"
                       decoration-danger="ozone_status == 'error'"
                       decoration-warning="ozone_status == 'disconnected'"/>
                <field name="odoo_response_time"/>
                <field name="ozone_response_time"/>
            </tree>
        </field>
    </record>

    <!-- Dashboard Kanban View -->
    <record id="view_connectivity_dashboard_kanban" model="ir.ui.view">
        <field name="name">connectivity.dashboard.kanban</field>
        <field name="model">connectivity.dashboard</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_dashboard" create="false" edit="false" delete="false">
                <field name="name"/>
                <field name="last_check"/>
                <field name="odoo_status"/>
                <field name="ozone_status"/>
                <field name="odoo_response_time"/>
                <field name="ozone_response_time"/>
                <field name="error_message"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                            <div class="o_kanban_image">
                                <i class="fa fa-dashboard fa-2x"/>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Odoo Status:</strong>
                                            <span t-attf-class="badge badge-pill badge-#{record.odoo_status.raw_value == 'connected' ? 'success' : (record.odoo_status.raw_value == 'error' ? 'danger' : 'warning')}">
                                                <field name="odoo_status"/>
                                            </span>
                                        </div>
                                        <div class="col-6">
                                            <strong>Ozone Status:</strong>
                                            <span t-attf-class="badge badge-pill badge-#{record.ozone_status.raw_value == 'connected' ? 'success' : (record.ozone_status.raw_value == 'error' ? 'danger' : 'warning')}">
                                                <field name="ozone_status"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="row mt-2" t-if="record.last_check.raw_value">
                                        <div class="col-12">
                                            <small>Last Check: <field name="last_check"/></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="action_connectivity_dashboard" model="ir.actions.act_window">
        <field name="name">Connectivity Dashboard</field>
        <field name="res_model">connectivity.dashboard</field>
        <field name="view_mode">kanban,form,tree</field>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Welcome to the Connectivity Dashboard!
            </p>
            <p>
                This dashboard monitors the connectivity status between Odoo and Ozone systems.
                Click "Refresh Status" to check the current connectivity.
            </p>
        </field>
    </record>
</odoo>
